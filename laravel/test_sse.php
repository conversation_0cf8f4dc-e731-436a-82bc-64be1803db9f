<?php

/**
 * Simple SSE test script to check if the endpoint is working
 */

// Get the access token from command line argument
if ($argc < 2) {
    echo "Usage: php test_sse.php <access_token>\n";
    exit(1);
}

$accessToken = $argv[1];
$url = "http://localhost/api/sse/progress?access_token=" . urlencode($accessToken);

echo "Testing SSE endpoint: $url\n";
echo "Press Ctrl+C to stop\n\n";

// Create a stream context with timeout
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => [
            'Accept: text/event-stream',
            'Cache-Control: no-cache',
        ],
        'timeout' => 60,
    ]
]);

// Open the SSE stream
$stream = fopen($url, 'r', false, $context);

if (!$stream) {
    echo "Failed to open SSE stream\n";
    exit(1);
}

echo "SSE stream opened successfully\n";
echo "Listening for events...\n\n";

// Read from the stream
while (!feof($stream)) {
    $line = fgets($stream);
    if ($line !== false) {
        echo "[" . date('Y-m-d H:i:s') . "] " . trim($line) . "\n";

        // If we get data, show it formatted
        if (strpos($line, 'data:') === 0) {
            $data = substr($line, 5);
            $decoded = json_decode(trim($data), true);
            if ($decoded) {
                echo "  Decoded: " . print_r($decoded, true) . "\n";
            }
        }
    }
}

fclose($stream);
echo "Stream closed\n";