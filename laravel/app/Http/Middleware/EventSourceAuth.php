<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Laravel\Passport\TokenRepository;
use App\User;

class EventSourceAuth
{
    protected $tokenRepository;

    public function __construct(TokenRepository $tokenRepository)
    {
        $this->tokenRepository = $tokenRepository;
    }

    public function handle(Request $request, Closure $next)
    {

        // Check if token is in query parameter
        if ($request->has('access_token')) {
            $token = $request->get('access_token');

            // Validate token format (basic check)
            if (empty($token) || strlen($token) < 10) {
                Log::error('EventSourceAuth: Invalid token format', [
                    'token_length' => strlen($token),
                    'token' => $token
                ]);
                return response()->json(['error' => 'Invalid access token format'], 401);
            }

            // Set Authorization header for compatibility
            $request->headers->set('Authorization', 'Bearer ' . $token);

            // Validate the token using Passport's TokenRepository and authenticate user
            try {
                // For JWT tokens, we need to decode to get the jti (token ID)
                $tokenParts = explode('.', $token);
                if (count($tokenParts) === 3) {
                    $payload = json_decode(base64_decode($tokenParts[1]), true);
                    $tokenId = $payload['jti'] ?? null;

                    if ($tokenId) {
                        $tokenModel = $this->tokenRepository->find($tokenId);
                        if (!$tokenModel) {
                            Log::error('EventSourceAuth: Token not found in database', [
                                'token_id' => $tokenId,
                                'token_prefix' => substr($token, 0, 10) . '...'
                            ]);
                            return response()->json(['error' => 'Invalid access token'], 401);
                        } else if ($tokenModel->revoked) {
                            Log::error('EventSourceAuth: Token is revoked', [
                                'token_id' => $tokenModel->id,
                                'token_prefix' => substr($token, 0, 10) . '...'
                            ]);
                            return response()->json(['error' => 'Token has been revoked'], 401);
                        } else if ($tokenModel->expires_at && $tokenModel->expires_at->isPast()) {
                            Log::error('EventSourceAuth: Token is expired', [
                                'token_id' => $tokenModel->id,
                                'expires_at' => $tokenModel->expires_at,
                                'token_prefix' => substr($token, 0, 10) . '...'
                            ]);
                            return response()->json(['error' => 'Token has expired'], 401);
                        } else {
                            // Token is valid, authenticate the user
                            $user = User::find($tokenModel->user_id);
                            if (!$user) {
                                Log::error('EventSourceAuth: User not found for token', [
                                    'token_id' => $tokenModel->id,
                                    'user_id' => $tokenModel->user_id,
                                ]);
                                return response()->json(['error' => 'User not found'], 401);
                            }

                            // Check if user is active
                            if ($user->status !== 'Active' && $user->status !== 'active') {
                                Log::error('EventSourceAuth: User is inactive', [
                                    'user_id' => $user->id,
                                    'status' => $user->status,
                                ]);
                                return response()->json(['error' => 'User account is inactive'], 401);
                            }

                            // Authenticate the user using Laravel's Auth system
                            Auth::guard('api')->setUser($user);

                            Log::info('EventSourceAuth: User authenticated successfully', [
                                'token_id' => $tokenModel->id,
                                'user_id' => $user->id,
                                'user_name' => $user->name,
                                'expires_at' => $tokenModel->expires_at,
                                'token_prefix' => substr($token, 0, 10) . '...'
                            ]);
                        }
                    } else {
                        Log::error('EventSourceAuth: No jti found in token payload', [
                            'token_prefix' => substr($token, 0, 10) . '...'
                        ]);
                        return response()->json(['error' => 'Invalid token format'], 401);
                    }
                } else {
                    Log::error('EventSourceAuth: Invalid JWT token format', [
                        'token_parts_count' => count($tokenParts),
                        'token_prefix' => substr($token, 0, 10) . '...'
                    ]);
                    return response()->json(['error' => 'Invalid token format'], 401);
                }
            } catch (\Exception $e) {
                Log::error('EventSourceAuth: Token validation failed', [
                    'error' => $e->getMessage(),
                    'token_prefix' => substr($token, 0, 10) . '...'
                ]);
                return response()->json(['error' => 'Token validation failed'], 401);
            }
        } else {
            Log::warning('EventSourceAuth: No access_token found in request', [
                'query_params' => $request->query(),
                'url' => $request->fullUrl()
            ]);
            return response()->json(['error' => 'Access token required'], 401);
        }

        return $next($request);
    }
}
